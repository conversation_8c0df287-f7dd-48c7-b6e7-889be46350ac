<template>
  <loading-overlay :loading="loading" />
  <Transition name="modal-confirm">
    <div v-if="props.open" class="modal-background-confirm">
      <div class="modal-confirm" ref="modal">
        <div class="modal-container-header">
          <div style="margin-right: auto">
            <h2>
              {{ props.modalTitle }}
            </h2>
          </div>
        </div>
        <div style="display: flex">
          <hr class="top-line" />
        </div>

        <template v-if="useReleaseNotificationsStore.releaseNotifications[0] && useReleaseNotificationsStore.isNotificationLength()">
          <div style="margin-right: auto">
            <h5>Poznámky k najnovšej verzií</h5>
          </div>
          <div class="release-notes-container">
            <v-card v-for="release in releaseNotes" :key="release.id_release_note" class="release-card" outlined>
              <v-card-title>{{ release.subject }}</v-card-title>
              <v-card-subtitle>{{ formatDate(release.release_date) }}</v-card-subtitle>
              <v-card-text>{{ release.description }}</v-card-text>
              <v-card-actions>
                <v-checkbox
                  v-model="readNotes"
                  :value="release.id_release_note"
                  label="Označiť ako prečítané"
                  color="#07327a"
                  :disabled="markAllAsUnread"
                ></v-checkbox>
              </v-card-actions>
            </v-card>
          </div>
          <v-checkbox v-model="markAllAsUnread" label="Označiť všetky ako prečítané" color="#07327a" @change="toggleAllReadStatus"></v-checkbox>
        </template>
        <!-- Latest Changes Section -->
        <template v-else>
          <div v-if="latestChanges.length > 0" class="latest-changes-section">
            <div class="latest-changes-header">
              <h5>Najnovšie zmeny</h5>
              <button class="view-all-btn" type="button" @click.stop="redirectToChangeLog()">Zobraziť všetky</button>
            </div>
            <div class="latest-changes-list">
              <v-card v-for="change in latestChanges" :key="change.id_log" class="change-card">
                <v-card-title class="change-card-title">
                  <span class="change-time">{{ formatDateTime(change.timestamp) }}</span>
                  <span class="change-reservation">{{ getFormattedClientName(change) }}</span>
                </v-card-title>
                <v-card-text>
                  <div class="change-user" v-if="change.doctor_name">
                    <span>
                      <b>Užívateľ:</b>
                      {{ change.doctor_name }}
                    </span>
                  </div>
                  <div class="change-user" v-if="change.service_name">
                    <span>
                      <b>Služba:</b>
                      {{ change.service_name }}
                    </span>
                  </div>
                  <div class="change-performed-by">
                    <span>
                      <b>Kto vykonal zmenu:</b>
                      <span class="performed-by-badge">{{ getPerformedByLabel(change.performed_by) }}</span>
                    </span>
                    <div v-if="change.performed_by && change.performed_by.name" class="performed-by-details">
                      <b>{{ change.performed_by.name }}</b>
                    </div>
                  </div>
                  <div class="change-details">
                    <div class="change-fields">
                      <span>
                        <b>{{ getChangeTypeLabel(change) }}</b>
                      </span>

                      <!-- Creation type - just show message -->
                      <div v-if="getChangeType(change) === 'create'" class="change-message">Rezervácia bola vytvorená</div>

                      <!-- Deletion type - show deleted status -->
                      <div v-else-if="getChangeType(change) === 'delete'" class="change-message">
                        <b class="deleted-status">
                          {{ useChangelogStore.translateValue(change.new_data.deleted_status, 'deleted_status') }}
                        </b>
                      </div>

                      <!-- Update or Notification - show changed fields -->
                      <template v-else>
                        <div v-if="specialChangeMessage(change)" class="change-message">
                          <b class="new-value" style="font-style: normal">{{ specialChangeMessage(change) }}</b>
                        </div>
                        <div v-for="(field, index) in getVisibleChangedFields(change)" :key="index" class="change-field">
                          <div class="field-name">{{ useChangelogStore.translateKey(field) }}</div>
                          <div class="field-values">
                            <span class="old-value">
                              <b>Pôvodne:</b>
                              {{ formatValue(useChangelogStore.translateValue(change.old_data[field], field)) }}
                            </span>
                            <span class="arrow">→</span>
                            <span class="new-value">
                              <b>Nová hodnota:</b>
                              {{ formatValue(useChangelogStore.translateValue(change.new_data[field], field)) }}
                            </span>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </div>
          </div>
          <div v-else class="no-changes-message">
            <p>Dnes neboli vykonané žiadne zmeny rezervácií</p>
          </div>
        </template>
        <div class="modal-container-footer">
          <div class="buttons-modal-container">
            <button class="modal-btn-confirm" type="button" @click="confirmModal()" :disabled="isInProgress">
              {{ closeModalText }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
  import { onClickOutside } from '@vueuse/core';
  import { ref, onMounted, watchEffect, computed, watch } from 'vue';
  import { useReleaseNotifications } from '../stores/release-notifications.js';
  import { useEmployee } from '../stores/employee';
  import { useRouter } from 'vue-router';
  import { useChangelog } from '../stores/changelogs.js';
  import { handleToastError, handleToastSuccess } from '../utility/notification.js';
  const useChangelogStore = useChangelog();
  const router = useRouter();
  const useEmployeeStore = useEmployee();
  const useReleaseNotificationsStore = useReleaseNotifications();
  const clientNameMap = ref({});
  const emits = defineEmits(['closeSimpleModal']);

  const props = defineProps({
    modalTitle: String,
    open: Boolean,
    type: String
  });

  const closeModalText = computed(() => {
    if (readNotes.value.length > 0) {
      return 'Uložiť';
    } else {
      return 'Zavrieť';
    }
  });
  const readNotes = ref([]);
  const loading = ref(false);
  const modal = ref(null);
  const isInProgress = ref(false);
  const markAllAsUnread = ref(false);

  const releaseNotes = computed(() => {
    if (useReleaseNotificationsStore.releaseNotifications[0]) {
      return useReleaseNotificationsStore.releaseNotifications[0];
    } else return [];
  });
  const confirmModal = async () => {
    isInProgress.value = true;
    if (readNotes.value.length > 0) {
      const res = await useReleaseNotificationsStore.markAsRead(readNotes.value);
      if (res && res.status === 200 && useEmployeeStore.myId) {
        await useReleaseNotificationsStore.getReleaseNotifications(useEmployeeStore.myId);
        if (closeModalText.value === 'Zavrieť') {
          closeModal();
        }
      }
    }
    isInProgress.value = false;
    if (closeModalText.value === 'Zavrieť') {
      closeModal();
    }
  };
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const resetNoClose = () => {
    markAllAsUnread.value = false;
    readNotes.value = [];
  };

  const closeModal = () => {
    markAllAsUnread.value = false;
    readNotes.value = [];
    emits('closeSimpleModal');
  };
  const toggleAllReadStatus = () => {
    if (markAllAsUnread.value) {
      readNotes.value = releaseNotes.value.map((release) => release.id_release_note);
    } else {
      readNotes.value = [];
    }
  };
  onClickOutside(modal, () => closeModal());
  const redirectToChangeLog = () => {
    emits('closeSimpleModal');
    router.push({ name: 'zmeny-rezervacie' });
  };

  const latestChanges = computed(() => {
    if (useChangelogStore.todaysReservationsChangelog && useChangelogStore.todaysReservationsChangelog.length > 0) {
      // Sort by timestamp (newest first)
      const allChanges = [...useChangelogStore.todaysReservationsChangelog]
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        // Filter out changes that should not be displayed
        .filter((log) => {
          // Always show creation and deletion events
          if (getChangeType(log) === 'create' || getChangeType(log) === 'delete') {
            return true;
          }

          // For other events, check if they contain meaningful changes
          return useChangelogStore.shouldDisplayChange(log);
        });

      return allChanges.slice(0, 3);
    }
    return [];
  });

  const getVisibleChangedFields = (change) => {
    return useChangelogStore.getFilteredChangedFields(change);
  };

  // Get special message for certain change types
  const specialChangeMessage = (change) => {
    return useChangelogStore.getSpecialChangeMessage(change.old_data, change.new_data);
  };

  // Format value for display
  const formatValue = (value) => {
    if (value === null || value === undefined) {
      return 'Prázdne';
    } else if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return value.toString();
  };
  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('sk-SK', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getChangeType = (change) => {
    // Use the direct log_type field from the backend
    switch (change.log_type) {
      case 'Created':
        return 'create';
      case 'Canceled':
        return 'delete';
      case 'Notified':
        return 'notification';
      case 'Updated':
      default:
        return 'update';
    }
  };

  const getChangeTypeLabel = (change) => {
    switch (change.log_type) {
      case 'Created':
        return 'Vytvorenie rezervácie';
      case 'Canceled':
        return 'Zmazanie rezervácie';
      case 'Notified':
        return 'Poslanie notifikácie';
      case 'Updated':
        return 'Aktualizovanie údajov rezervácie';
      default:
        return 'Zmenené:';
    }
  };

  const getFormattedClientName = (log) => {
    if (log.first_name && log.last_name) {
      return `${log.first_name} ${log.last_name}`;
    }
    return `ID: ${log.table_primary_key}`;
  };
  const app = document.getElementById('app');
  watchEffect(() => {
    if (props.open) {
      if (app) {
        app.style.overflow = 'hidden';
      }
    } else {
      if (app) {
        app.style.overflow = 'visible';
      }
    }
  });

  // Add helper for performed_by translation
  const getPerformedByLabel = (performed_by) => {
    if (!performed_by || !performed_by.type) return '-';
    switch (performed_by.type) {
      case 'system':
        return 'Systém';
      case 'user':
        return 'Používateľ';
      case 'doctor':
        return 'Lekár';
      case 'client':
        return 'Klient';
      default:
        return '-';
    }
  };
</script>

<style scoped lang="scss">
  .modal-btn-confirm {
    background-color: var(--blue);
    border: 0;
    padding: 20px 20px 20px 20px;
    color: white;
    border-radius: 30px;
    text-align: center;
    font-size: 12px;
    line-height: 15px;
    text-transform: uppercase;
    margin-top: 2.5em;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-right: 0.5em;
    width: 100px;
  }

  .modal-btn-confirm:hover {
    cursor: pointer;
    opacity: 0.7;
  }

  .modal-background-confirm {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    z-index: 99;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .modal-confirm {
    right: 0;
    background: white;
    padding: 2em;
    position: fixed;
    margin: auto;
    max-width: 55em;
    top: 5em;
    left: 0;
    width: 55em;
    max-height: 90vh;
    z-index: 9999;
    border-radius: 20px;
    overflow-y: auto;
  }
  .close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    cursor: pointer;
  }

  .modal-confirm-enter-active,
  .modal-confirm-leave-active {
    transition: 0.2s ease-out;
  }

  .modal-confirm-enter-from,
  .modal-confirm-leave-to {
    opacity: 0;
    transition: 0.2s ease-out;
  }
  .modal-container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .modal-container-footer {
    display: flex;
    justify-content: space-between;
  }
  .release-notes-container {
    overflow-y: auto;
    padding: 1em;
    margin-top: 2em;
  }

  .release-card {
    margin-bottom: 1em;
    padding: 1em;
    border-radius: 10px;
  }

  .latest-changes-section {
    margin-top: 1em;
    margin-bottom: 2em;
  }

  .latest-changes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
  }

  .view-all-btn {
    background-color: transparent;
    border: none;
    color: var(--blue);
    font-weight: 600;
    cursor: pointer;
    padding: 5px 10px;
    font-size: 14px;

    &:hover {
      text-decoration: underline;
    }
  }

  .latest-changes-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .change-card {
    border-radius: 8px;
    padding: 8px;

    display: flex;
    flex-direction: column;
  }
  .change-card .v-card-text {
    overflow-y: auto;
    flex-grow: 1;
    padding-bottom: 8px; /* Reduce bottom padding */
  }
  .change-card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    padding: 0px 0px 8px 16px;
    flex-shrink: 0; /* Prevent the title from shrinking */
  }

  .change-time {
    font-weight: bold;
    color: var(--blue);
  }

  .change-reservation {
    background-color: #f5f5f5;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .change-user {
    margin-bottom: 8px;
    font-size: 14px;
  }

  .change-performed-by {
    margin-bottom: 8px;
    font-size: 14px;
  }

  .performed-by-badge {
    background-color: #e3f2fd;
    color: var(--blue);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    margin-left: 4px;
  }

  .performed-by-details {
    margin-top: 4px;
    margin-left: 4px;
  }

  .performed-by-name {
    font-size: 12px;
    color: #666;
    font-style: italic;
  }

  .change-details {
    font-size: 14px;
  }

  .change-fields {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .change-field {
    background-color: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    margin-top: 4px;
    display: inline-block;
  }

  .no-changes-message {
    text-align: center;
    margin: 2em 0;
    color: #666;
  }

  .no-changes {
    color: #666;
    font-style: italic;
  }

  @media screen and (max-width: 685px) {
    .modal-confirm {
      width: 20em;
      max-height: 90vh; /* Increased from 100em to 90vh */
      max-width: 90%;
      top: 2em;
    }
    .modal-container-footer {
      margin-top: 1em;
    }
    .buttons-modal-container {
      margin-bottom: -1em;
      text-align: center;
    }
    .modal-btn-confirm {
      background-color: var(--blue);
      border: 0;
      padding: 15px;
      color: white;
      border-radius: 30px;
      text-align: center;
      font-size: 12px;
      line-height: 15px;
      text-transform: uppercase;
      margin-top: 2em;
      font-weight: 600;
      transition: all 0.3s ease;
      margin-right: 0;
    }
    .check-container {
      display: block;
      margin-top: 0.5em;
    }
    .latest-changes-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .change-card-title {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .change-reservation {
      align-self: flex-end;
    }

    .change-card {
      max-height: 220px; /* Taller cards on mobile due to wrapping */
    }
  }

  .top {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 1.5em;
    margin-top: -2.5em;
  }

  .top-line {
    width: 99%;
    text-align: left;
    margin-left: 0;
    margin-top: 1.5em;
    margin-bottom: 2em;
  }

  hr.top-line {
    color: #e1e1e1;
    border: 1px solid;
  }

  .redirect-btn {
    background-color: var(--blue);
    border: 0;
    padding: 15px;
    color: white;
    border-radius: 30px;
    text-align: center;
    font-size: 12px;
    line-height: 15px;
    text-transform: uppercase;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 160px;
  }
  .redirect-btn:hover {
    cursor: pointer;
    opacity: 0.7;
  }
  .reconstructed-data-section {
    margin-top: 16px;
    padding: 12px;
    border-radius: 4px;
    border-left: 3px solid #d32f2f;
  }

  .reconstructed-value {
    color: #d32f2f;
    font-weight: bold;
  }

  .change-field-group {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .new-value-create {
    color: #2e7d32;
    font-weight: bold;
  }

  .deleted-status {
    color: #d32f2f;
    font-style: normal;
  }

  .change-message {
    margin-top: 10px;
    padding: 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-style: italic;
  }
  .field-name {
    font-weight: bold;
    margin-bottom: 4px;
  }

  .field-values {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
  }

  .old-value {
    color: #d32f2f;
    padding: 2px 4px;
    border-radius: 3px;
  }

  .new-value {
    color: #2e7d32;
    padding: 2px 4px;
    border-radius: 3px;
  }

  .arrow {
    font-weight: bold;
    color: #666;
  }

  .change-message {
    margin-top: 10px;
    padding: 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-style: italic;
  }

  .deleted-status {
    color: #d32f2f;
    font-style: normal;
  }

  /* Responsive adjustments */
  @media screen and (max-width: 685px) {
    .field-values {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .arrow {
      display: none;
    }
  }
</style>
