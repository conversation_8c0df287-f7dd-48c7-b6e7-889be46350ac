<template>
  <div>
    <loading-overlay :loading="loading" />
    <div style="padding-right: 16px">
      <div class="select-area-adresy">
        <div class="search-container">
          <input v-model="search" id="search_table" placeholder="Hľadať" type="text" />
          <v-btn icon class="ml-2" @click="showDateDialog = true" size="small">
            <v-icon size="24" color="#07327a">mdi-calendar</v-icon>
          </v-btn>
          <v-btn icon class="ml-1" @click="clearDateFilter" size="small" :disabled="!dateFilterActive" :color="dateFilterActive ? '#07327a' : 'grey'">
            <v-icon size="24">mdi-filter-remove</v-icon>
          </v-btn>
          <div class="date-range-display ml-3">
            {{ formatDateRange }}
          </div>
        </div>
      </div>

      <v-dialog v-model="showDateDialog" max-width="500px">
        <v-card>
          <v-card-title class="text-center" style="padding-top: 1em">
            <h3>Zvoľte obdobie</h3>
          </v-card-title>
          <v-card-text>
            <div class="text-center">
              <DatePicker v-model="dateRange" color="blue" is-range style="margin-bottom: 1em"></DatePicker>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="#07327a" variant="text" @click="showDateDialog = false">Zavrieť</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-data-table-server
        item-value="id"
        :items="filteredReservationRows"
        :items-length="totalItems"
        :headers="headers"
        :loading="mountedLoading"
        v-model:items-per-page="itemsPerPage"
        v-model:page="currentPage"
        @update:options="handleOptionsChange"
        :items-per-page-options="[10, 25, 50, 100]"
        show-expand
      >
        <template v-slot:loading>
          <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
        </template>

        <!-- Expand button -->
        <template v-slot:item.data-table-expand="{ internalItem, isExpanded, toggleExpand }">
          <v-btn
            :append-icon="isExpanded(internalItem) ? 'mdi-chevron-up' : 'mdi-chevron-down'"
            :text="isExpanded(internalItem) ? 'Skryť históriu' : 'Zobraziť históriu'"
            class="text-none"
            color="medium-emphasis"
            size="small"
            variant="text"
            :border="true"
            @click="toggleExpand(internalItem)"
          ></v-btn>
        </template>

        <!-- Expanded row with changelog history -->
        <template v-slot:expanded-row="{ columns, item }">
          <tr>
            <td :colspan="columns.length" class="pa-4">
              <v-sheet rounded="lg" :border="true">
                <div class="pa-4">
                  <h3 class="mb-3">História zmien</h3>

                  <v-table density="compact" :sort-by="[{ key: 'timestamp', order: 'desc' }]">
                    <thead>
                      <tr>
                        <th>Dátum zmeny</th>
                        <th>Typ zmeny</th>
                        <th>Doktor</th>
                        <th>Kto vykonal zmenu</th>
                        <th>Staré údaje</th>
                        <th>Nové údaje</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(log, index) in item.changelog" :key="`${log.id_log}-${index}`">
                        <!-- Timestamp -->
                        <td class="text-center">
                          {{ new Date(log.timestamp).toLocaleString('sk-SK') }}
                        </td>
                        <!-- Change type -->
                        <td class="text-center">
                          <span class="change-type-label">{{ getChangeTypeLabel(log) }}</span>
                        </td>
                        <!-- Doctor -->
                        <td class="text-center">
                          <span>{{ log.doctor_name || '-' }}</span>
                        </td>
                        <!-- Performed by -->
                        <td class="text-center">
                          <span class="performed-by-badge">{{ getPerformedByLabel(log.performed_by) }}</span>
                          <span v-if="log.performed_by && log.performed_by.name" class="performed-by-name-inline">: {{ log.performed_by.name }}</span>
                        </td>
                        <!-- Old data -->
                        <td>
                          <!-- Creation: empty -->
                          <div v-if="getChangeType(log) === 'create'">
                            <span class="text-muted">-</span>
                          </div>

                          <!-- Deletion: show deleted status -->
                          <div v-else-if="getChangeType(log) === 'delete'">
                            <div>
                              <b>{{ useChangelogStore.translateKey('deleted_status') }}:</b>
                              <span class="deleted-status ml-2">
                                <b>{{ useChangelogStore.translateValue(log.new_data.deleted_status, 'deleted_status') }}</b>
                              </span>
                            </div>
                          </div>

                          <!-- Update/Notification: show old data -->
                          <div v-else-if="Object.keys(filteredOldData(log.old_data)).length > 0">
                            <div v-if="Object.keys(filteredOldData(log.old_data)).length <= 2">
                              <div v-for="(value, key) in filteredOldData(log.old_data)" :key="key">
                                <b>{{ useChangelogStore.translateKey(key) }}:</b>
                                <span :class="{ 'text-red': log.new_data[key] !== value }" style="margin-left: 0.5em">
                                  <b>{{ useChangelogStore.translateValue(value, key) }}</b>
                                </span>
                              </div>
                            </div>
                            <div v-else>
                              <v-menu :close-on-content-click="false" max-width="300">
                                <template v-slot:activator="{ props }">
                                  <v-btn color="#07327a" variant="text" v-bind="props">Zobraziť viac</v-btn>
                                </template>
                                <v-card class="compact-menu-card">
                                  <v-card-text class="pa-2" style="max-height: 300px; overflow-y: auto">
                                    <v-list density="compact" class="compact-list">
                                      <v-list-item v-for="(value, key) in filteredOldData(log.old_data)" :key="key" class="compact-list-item">
                                        <v-list-item-title>
                                          <b>{{ useChangelogStore.translateKey(key) }}:</b>
                                          <span :class="{ 'text-red': log.new_data[key] !== value }" style="margin-left: 0.5em">
                                            <b>{{ useChangelogStore.translateValue(value, key) }}</b>
                                          </span>
                                        </v-list-item-title>
                                      </v-list-item>
                                    </v-list>
                                  </v-card-text>
                                </v-card>
                              </v-menu>
                            </div>
                          </div>
                          <div v-else class="text-muted">-</div>
                        </td>

                        <!-- New data -->
                        <td>
                          <!-- Creation: show relevant fields -->
                          <div v-if="getChangeType(log) === 'create'">
                            <div v-if="Object.keys(getRelevantCreationFields(log.new_data)).length <= 2">
                              <div v-for="(value, key) in getRelevantCreationFields(log.new_data)" :key="key">
                                <b>{{ useChangelogStore.translateKey(key) }}:</b>
                                <span class="text-green" style="margin-left: 0.5em">
                                  <b>{{ useChangelogStore.translateValue(value, key) }}</b>
                                </span>
                              </div>
                            </div>
                            <div v-else>
                              <v-menu :close-on-content-click="false" max-width="300">
                                <template v-slot:activator="{ props }">
                                  <v-btn color="#07327a" variant="text" v-bind="props">Zobraziť viac</v-btn>
                                </template>
                                <v-card class="compact-menu-card">
                                  <v-card-text class="pa-2" style="max-height: 300px; overflow-y: auto">
                                    <v-list density="compact" class="compact-list">
                                      <v-list-item v-for="(value, key) in getRelevantCreationFields(log.new_data)" :key="key" class="compact-list-item">
                                        <v-list-item-title>
                                          <b>{{ useChangelogStore.translateKey(key) }}:</b>
                                          <span class="text-green" style="margin-left: 0.5em">
                                            <b>{{ useChangelogStore.translateValue(value, key) }}</b>
                                          </span>
                                        </v-list-item-title>
                                      </v-list-item>
                                    </v-list>
                                  </v-card-text>
                                </v-card>
                              </v-menu>
                            </div>
                          </div>

                          <!-- Deletion: show reconstructed data -->
                          <div v-else-if="getChangeType(log) === 'delete'">
                            <div v-if="Object.keys(getReconstructedData(log)).length <= 2">
                              <div v-for="(value, key) in getReconstructedData(log)" :key="key">
                                <b>{{ useChangelogStore.translateKey(key) }}:</b>
                                <span class="reconstructed-value" style="margin-left: 0.5em">
                                  <b>{{ useChangelogStore.translateValue(value, key) }}</b>
                                </span>
                              </div>
                            </div>
                            <div v-else-if="Object.keys(getReconstructedData(log)).length > 0">
                              <v-menu :close-on-content-click="false" max-width="300">
                                <template v-slot:activator="{ props }">
                                  <v-btn color="#07327a" variant="text" v-bind="props">Zobraziť údaje pred zmazaním</v-btn>
                                </template>
                                <v-card class="compact-menu-card">
                                  <v-card-text class="pa-2" style="max-height: 300px; overflow-y: auto">
                                    <v-list density="compact" class="compact-list">
                                      <v-list-item v-for="(value, key) in getReconstructedData(log)" :key="key" class="compact-list-item">
                                        <v-list-item-title>
                                          <b>{{ useChangelogStore.translateKey(key) }}:</b>
                                          <span class="reconstructed-value" style="margin-left: 0.5em">
                                            <b>{{ useChangelogStore.translateValue(value, key) }}</b>
                                          </span>
                                        </v-list-item-title>
                                      </v-list-item>
                                    </v-list>
                                  </v-card-text>
                                </v-card>
                              </v-menu>
                            </div>
                            <div v-else class="text-muted">Žiadne údaje k dispozícii</div>
                          </div>

                          <!-- Special state changes with fixed messages -->
                          <div v-else-if="specialChangeMessage(log)" class="special-message">
                            <b>{{ specialChangeMessage(log) }}</b>
                          </div>

                          <!-- Update/Notification: show new data -->
                          <div v-else>
                            <div v-if="Object.keys(filteredNewData(log.new_data)).length <= 2">
                              <div v-for="(value, key) in filteredNewData(log.new_data)" :key="key">
                                <b>{{ useChangelogStore.translateKey(key) }}:</b>
                                <span :class="{ 'text-green': log.old_data[key] !== value }" style="margin-left: 0.5em">
                                  <b v-if="value !== null && value !== ''">{{ useChangelogStore.translateValue(value, key) }}</b>
                                  <b v-else class="text-red">/</b>
                                </span>
                              </div>
                            </div>
                            <div v-else>
                              <v-menu :close-on-content-click="false" max-width="300">
                                <template v-slot:activator="{ props }">
                                  <v-btn color="#07327a" variant="text" v-bind="props">Zobraziť viac</v-btn>
                                </template>
                                <v-card class="compact-menu-card">
                                  <v-card-text class="pa-2" style="max-height: 300px; overflow-y: auto">
                                    <v-list density="compact" class="compact-list">
                                      <v-list-item v-for="(value, key) in filteredNewData(log.new_data)" :key="key" class="compact-list-item">
                                        <v-list-item-title>
                                          <b>{{ useChangelogStore.translateKey(key) }}:</b>
                                          <span :class="{ 'text-green': log.old_data[key] !== value }" style="margin-left: 0.5em">
                                            <b v-if="value !== null && value !== ''">{{ useChangelogStore.translateValue(value, key) }}</b>
                                            <b v-else class="text-red">/</b>
                                          </span>
                                        </v-list-item-title>
                                      </v-list-item>
                                    </v-list>
                                  </v-card-text>
                                </v-card>
                              </v-menu>
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </v-table>
                </div>
              </v-sheet>
            </td>
          </tr>
        </template>
      </v-data-table-server>
    </div>
  </div>
</template>

<script setup>
  import { DatePicker } from 'v-calendar';
  import 'v-calendar/dist/style.css';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useChangelog } from '../../stores/changelogs.js';
  const useChangelogStore = useChangelog();
  const loading = ref(false);
  const search = ref('');
  const showDateDialog = ref(false);

  const getReservationLogs = computed(() => {
    if (useChangelogStore.getReservationChangelogs.length === 0) return [];
    return useChangelogStore.getReservationChangelogs;
  });

  const specialChangeMessage = (log) => {
    return useChangelogStore.getSpecialChangeMessage(log.old_data, log.new_data);
  };

  const getFormattedClientName = (log) => {
    if (log.first_name && log.last_name) {
      return `${log.first_name} ${log.last_name}`;
    }
    return `ID: ${log.table_primary_key}`;
  };

  const headers = [
    {
      title: 'Klient',
      key: 'client_name',
      align: 'start',
      sortable: true
    },
    {
      title: 'Služba',
      key: 'service_name',
      align: 'start',
      sortable: true
    },
    {
      title: 'Dátum konania',
      key: 'date_of_reservation',
      align: 'center',
      sortable: true
    },
    {
      title: 'Posledná zmena',
      key: 'last_update',
      align: 'center',
      sortable: true
    },
    {
      title: 'Doktor',
      key: 'doctor',
      align: 'center',
      sortable: true
    },
    {
      title: '',
      key: 'data-table-expand',
      sortable: false
    }
  ];

  // Get reservation date for log entry
  const getReservationDateForLog = (log) => {
    const id = log.table_primary_key;
    if (log.date_of_reservation) {
      return formatReservationDate(log.date_of_reservation);
    }

    // Then check if we have a creation log with date info
    const creationLog = getReservationLogs.value
      .filter((l) => l.table_primary_key === id && getChangeType(l) === 'create')
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))[0];

    if (creationLog && creationLog.date_of_reservation) {
      return formatReservationDate(creationLog.date_of_reservation);
    }

    // Finally check if we have any reservation state data
    if (reservationStates.value[id] && reservationStates.value[id].date_of_reservation) {
      return formatReservationDate(reservationStates.value[id].date_of_reservation);
    }

    return '-';
  };

  // Use useChangelogStore's filterChangelogData instead of local function
  const filteredNewData = (data) => {
    return useChangelogStore.filterChangelogData(data);
  };
  const filteredOldData = (data) => {
    return useChangelogStore.filterChangelogData(data);
  };
  const dateRange = ref({
    start: new Date(new Date().setDate(new Date().getDate() - 7)),
    end: new Date()
  });

  const mountedLoading = ref(false);
  const currentPage = ref(1);
  const itemsPerPage = ref(25);
  const totalItems = ref(0);
  const lastFetchedPage = ref(1);
  const lastFetchedItemsPerPage = ref(25);
  const handleOptionsChange = (options) => {
    // Compare with last fetched values, not the current values
    if (options.itemsPerPage !== lastFetchedItemsPerPage.value || options.page !== lastFetchedPage.value) {
      itemsPerPage.value = options.itemsPerPage;
      currentPage.value = options.page;
      loadPaginatedData();
    }
  };

  const loadPaginatedData = async () => {
    mountedLoading.value = true;
    try {
      // Save current pagination state as "last fetched"
      lastFetchedPage.value = currentPage.value;
      lastFetchedItemsPerPage.value = itemsPerPage.value;

      const result = await useChangelogStore.getAllReservationsChangelogs(dateRange.value.start, dateRange.value.end, currentPage.value, itemsPerPage.value);

      if (result) {
        // Use total_count from the API response
        totalItems.value = result.total_count;

        // Optionally sync current page with server (in case server has different pagination logic)
        if (result.current_page) {
          currentPage.value = result.current_page;
          lastFetchedPage.value = result.current_page;
        }
      }

      buildReservationStates();
    } finally {
      mountedLoading.value = false;
    }
  };

  onMounted(async () => {
    mountedLoading.value = true;
    try {
      await loadPaginatedData();
    } finally {
      mountedLoading.value = false;
    }
  });

  watch(dateRange, () => {
    getNewDates();
  });

  const getNewDates = () => {
    parseDates(dateRange.value);
  };

  const parseDates = async (myDates) => {
    mountedLoading.value = true;
    showDateDialog.value = false;
    try {
      // Reset pagination when date range changes
      currentPage.value = 1;
      lastFetchedPage.value = 1;

      await useChangelogStore.getAllReservationsChangelogs(myDates.start, myDates.end, currentPage.value, itemsPerPage.value);

      // Update last fetched items per page
      lastFetchedItemsPerPage.value = itemsPerPage.value;

      buildReservationStates();
    } finally {
      mountedLoading.value = false;
    }
  };

  const formatDateRange = computed(() => {
    if (!dateRange.value.start || !dateRange.value.end) return '';

    const formatDate = (date) => {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}. ${month}. ${year}`;
    };

    return `${formatDate(dateRange.value.start)} - ${formatDate(dateRange.value.end)}`;
  });

  const getChangeType = (log) => {
    // Use the direct log_type field from the backend
    switch (log.log_type) {
      case 'Created':
        return 'create';
      case 'Canceled':
        return 'delete';
      case 'Notified':
        return 'notification';
      case 'Updated':
      default:
        return 'update';
    }
  };

  // Add helper function to get appropriate label for each type
  const getChangeTypeLabel = (log) => {
    switch (log.log_type) {
      case 'Created':
        return 'Vytvorenie rezervácie';
      case 'Canceled':
        return 'Zmazanie rezervácie';
      case 'Notified':
        return 'Poslanie notifikácie';
      case 'Updated':
        return 'Aktualizovanie údajov rezervácie';
      default:
        return 'Zmenené:';
    }
  };
  // Add these functions from reservation-change-log.vue
  // Store reconstructed reservation data for each reservation ID
  const reservationStates = ref({});

  // Build the complete state of each reservation at each point in time
  const buildReservationStates = () => {
    if (!getReservationLogs.value || getReservationLogs.value.length === 0) return;

    // Process logs for each unique reservation ID
    const reservationIds = [...new Set(getReservationLogs.value.map((log) => log.table_primary_key))];

    reservationIds.forEach((id) => {
      // Get all logs for this reservation, sorted chronologically (oldest first)
      const sortedLogs = getReservationLogs.value.filter((log) => log.table_primary_key === id).sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

      // Initialize state object for this reservation
      reservationStates.value[id] = {};

      // Process each log in chronological order
      sortedLogs.forEach((log) => {
        // For creation, set the initial state
        if (getChangeType(log) === 'create') {
          // Add all non-null values from the creation event
          for (const [key, value] of Object.entries(log.new_data)) {
            if (value !== null && value !== '') {
              reservationStates.value[id][key] = value;
            }
          }
        }
        // For updates, update the specific fields that changed
        else if (getChangeType(log) === 'update' || getChangeType(log) === 'notification') {
          for (const [key, value] of Object.entries(log.new_data)) {
            // Update the state with the new value
            reservationStates.value[id][key] = value;
          }
        }
      });
    });
  };

  // Get the reconstructed reservation data at the time of deletion
  const getReconstructedData = (deleteLog) => {
    const id = deleteLog.table_primary_key;

    // If we have built the state for this reservation
    if (reservationStates.value[id]) {
      // Get all non-system fields that have non-null values
      return getRelevantFields(reservationStates.value[id]);
    }

    return {};
  };

  // Get relevant fields (non-system, non-null) from a data object
  const getRelevantFields = (data) => {
    const relevantFields = {};
    const ignoredFields = [
      'id_client',
      'id_service',
      'id_deleted_user',
      'consent_personal_data',
      'consent_terms_conditions',
      'cancelHash',
      'number_of_send_email',
      'number_of_send_sms',
      'created_by_assistant',
      'created_by_user_id',
      'deleted_status',
      'reservation_status',
      'arrive_status',
      'id_deleted_user',
      'canceled_at',
      'updated_at'
    ];

    for (const key in data) {
      if (!ignoredFields.includes(key) && data[key] !== null && data[key] !== '') {
        relevantFields[key] = data[key];
      }
    }

    return relevantFields;
  };

  // Get relevant fields for creation display
  const getRelevantCreationFields = (newData) => {
    const relevantFields = {};
    const ignoredFields = [
      'id_client',
      'id_service',
      'id_deleted_user',
      'consent_personal_data',
      'consent_terms_conditions',
      'cancelHash',
      'number_of_send_email',
      'number_of_send_sms',
      'unique_code',
      'created_by_assistant',
      'created_by_user_id',
      'updated_at'
    ];

    for (const key in newData) {
      if (!ignoredFields.includes(key) && newData[key] !== null && newData[key] !== '') {
        relevantFields[key] = newData[key];
      }
    }

    return relevantFields;
  };

  // Add this new computed property to extract latest logs for each group
  const latestLogsMap = computed(() => {
    const result = {};

    if (!getReservationLogs.value || getReservationLogs.value.length === 0) return result;

    // Get unique reservation IDs
    const reservationIds = [...new Set(getReservationLogs.value.map((log) => log.table_primary_key))];

    reservationIds.forEach((id) => {
      // Get all logs for this reservation
      const reservationLogs = getReservationLogs.value.filter((log) => log.table_primary_key === id);

      if (reservationLogs.length === 0) return;

      // Sort by timestamp (newest first)
      const sortedLogs = [...reservationLogs].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      // Get the latest log
      const latestLog = sortedLogs[0];

      // Save the latest log for this reservation
      result[id] = latestLog;

      // Find the most recent reservation date
      // First check for creation events
      const creationLog = sortedLogs.find((log) => getChangeType(log) === 'create');

      if (creationLog && creationLog.new_data.date_of_reservation) {
        // Use the creation date if available
        result[id].reservation_date = creationLog.new_data.date_of_reservation;
      } else {
        // Otherwise, look for the most recent update to date_of_reservation
        const dateUpdateLog = sortedLogs.find(
          (log) => log.new_data.date_of_reservation && log.old_data.date_of_reservation && log.new_data.date_of_reservation !== log.old_data.date_of_reservation
        );

        if (dateUpdateLog) {
          result[id].reservation_date = dateUpdateLog.new_data.date_of_reservation;
        } else {
          // If no specific date update found, check if any log has the date
          const anyLogWithDate = sortedLogs.find((log) => log.new_data.date_of_reservation);
          result[id].reservation_date = anyLogWithDate?.new_data.date_of_reservation || null;
        }
      }
    });

    return result;
  });

  // Add a helper function to format the reservation date
  const formatReservationDate = (dateString) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('sk-SK');
    } catch (e) {
      return dateString;
    }
  };

  const reservationRows = computed(() => {
    if (!getReservationLogs.value || getReservationLogs.value.length === 0) return [];

    const groupedLogs = {};
    getReservationLogs.value.forEach((log) => {
      const id = log.table_primary_key;
      if (!groupedLogs[id]) {
        groupedLogs[id] = [];
      }
      groupedLogs[id].push(log);
    });

    const result = [];

    Object.keys(groupedLogs).forEach((id) => {
      // Sort logs by timestamp (newest first)
      const sortedLogs = [...groupedLogs[id]].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      // Skip if no valid logs
      if (sortedLogs.length === 0) return;

      // Get the latest log (it will have the most current information)
      const latestLog = sortedLogs[0];

      // Filter logs to only include those that should be displayed
      const displayableLogs = sortedLogs.filter((log) => {
        // Always show creation and deletion events
        if (getChangeType(log) === 'create' || getChangeType(log) === 'delete') {
          return true;
        }

        // For other events, check if they contain meaningful changes
        return useChangelogStore.shouldDisplayChange(log);
      });

      // Skip if no displayable logs
      if (displayableLogs.length === 0) return;

      // Create parent row using latest state
      const parentRow = {
        id: id,
        table_primary_key: id,
        client_name: getFormattedClientName(latestLog),
        service_name: latestLog.service_name || '-',
        date_of_reservation: getReservationDateForLog(latestLog),
        non_filtered_update: latestLog.updated_at,
        last_update: new Date(latestLog.timestamp).toLocaleString('sk-SK'),
        doctor: latestLog.doctor_name || '-',
        // Store the changelog history for expansion - using filtered logs
        changelog: displayableLogs,
        // Store reconstructed data
        reconstructed_data: getReconstructedData({ table_primary_key: id })
      };

      result.push(parentRow);
    });

    // Sort result by most recent update
    return result.sort((a, b) => {
      const dateA = new Date(a.non_filtered_update);
      const dateB = new Date(b.non_filtered_update);
      return dateB - dateA;
    });
  });

  // Filter with search
  const filteredReservationRows = computed(() => {
    if (!search.value) return reservationRows.value;

    return reservationRows.value.filter((row) => {
      const rowStr = JSON.stringify(row);
      const searchLower = search.value.toLowerCase();
      return rowStr.toLowerCase().includes(searchLower);
    });
  });

  // Add this to your ref declarations
  const dateFilterActive = computed(() => {
    return dateRange.value.start && dateRange.value.end && !isDefaultDateRange(dateRange.value.start, dateRange.value.end);
  });

  // Add this method to clear the date filter and reset to the default week
  const clearDateFilter = async () => {
    mountedLoading.value = true;

    // Reset to default (last 7 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);

    dateRange.value = {
      start: startDate,
      end: endDate
    };

    // Reset pagination
    currentPage.value = 1;
    lastFetchedPage.value = 1;

    // Fetch data with the new date range
    await loadPaginatedData();

    showDateDialog.value = false;
    mountedLoading.value = false;
  };

  // Helper function to determine if current range is the default range
  const isDefaultDateRange = (start, end) => {
    const endDate = new Date(end);
    const startDate = new Date(start);
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 7);

    // Compare dates (ignoring time)
    return endDate.toDateString() === new Date().toDateString() && startDate.toDateString() === defaultStartDate.toDateString();
  };

  // Add helper for performed_by translation
  const getPerformedByLabel = (performed_by) => {
    if (!performed_by || !performed_by.type) return '-';
    switch (performed_by.type) {
      case 'system':
        return 'Systém';
      case 'user':
        return 'Používateľ';
      case 'doctor':
        return 'Lekár';
      case 'client':
        return 'Klient';
      default:
        return '-';
    }
  };
</script>

<style lang="scss" scoped>
  :deep(.v-data-table) {
    tbody tr {
      cursor: pointer;

      &:hover {
        background-color: rgba(7, 50, 122, 0.05);
      }
    }

    th {
      font-weight: 700 !important;
      font-size: 0.9rem !important;
    }
    .v-data-table__thead {
      background-color: rgba(25, 118, 210, 0.05);
    }
  }
  h1,
  h2 {
    text-align: center;
    line-height: 1.3;
  }

  .flex-center {
    display: flex;
    justify-content: center;
  }

  label {
    font-weight: 600;
    display: flex;
    align-items: center;
  }

  .select-container {
    padding: 0.2em 0.75em;
    border-radius: 3px;
    background: rgba(202, 198, 195, 0.575);
  }

  .select-container:hover {
    background: rgba(202, 198, 195, 0.764);
  }

  .graphs-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-bottom: 5rem;
    gap: 2em;
    margin-right: 1em;
  }

  .graph-desc {
    margin: 0.75em 0;
    text-align: center;
  }

  .graph {
    margin-bottom: 6em;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 90%;
    height: 350px;
  }

  .table-cointainer {
    margin-bottom: 2em;
  }

  v-table {
    border-collapse: collapse;
  }

  th,
  td {
    text-align: center !important;
    padding: 0.5em;
  }

  thead {
    background: #052e73;
    color: white;
  }

  .tables {
    max-width: 800px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .table-wrapper {
    width: 100%;
  }

  .table-header {
    margin-bottom: 0.2em;
  }

  .graph-pie {
    margin-bottom: 3em;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 90%;
  }

  .graph-pie-total {
    margin-top: 2em;
  }

  .graph-charts {
    display: flex;
    flex-wrap: wrap;
    gap: 1em;
    justify-content: center;
    align-items: center;
  }

  .graph-chart {
    flex: 1;
    width: calc(50% - 1em);
  }
  .employee-select {
    min-width: 250px;
    max-width: 400px;
  }
  .select-area-statistics {
    border: none;
    position: relative;
    display: flex;
    justify-content: space-between;
  }
  @media (max-width: 940px) {
    .graph-charts {
      flex-direction: column;
    }

    .graph-chart {
      width: 100%;
    }

    .graph-smaller-legend {
      padding-right: 5.7em;
    }
  }

  @media (min-width: 1800px) {
    .graph-pie {
      width: 70%;
    }

    .graph-pie-total {
      width: 60%;
    }
  }

  @media (min-width: 2000px) {
    .graph-pie {
      max-width: 1500px;
    }
  }

  @media screen and (max-width: 990px) {
    .graph {
      flex-direction: column;
      width: 100%;
    }

    .graph-mob {
      flex-direction: column;
      align-items: center;
      width: 90% !important;
    }

    .table-cointainer {
      padding-right: 1em;
      width: 100%;
    }
  }

  @media screen and (max-width: 400px) {
    .graph-desc {
      font-size: 1em;
      margin-inline: 1em;
    }

    .graph-pie-total {
      width: 100%;
      padding-left: 1em;
      margin-top: 6em;
    }
  }

  @media screen and (max-width: 673px) {
    .tables {
      margin-top: 2em;
    }
    .employee-select {
      margin-right: 1em;
    }
    .select-area-statistics {
      display: block;
    }
    .graph-smaller-legend {
      padding: 0;
    }

    .graph-chart {
      flex: none;
      height: 400px;
      width: 90%;
    }

    .graphs-wrapper {
      gap: 0;
    }

    .graph-pie-total {
      margin-top: 6.5em;
    }
  }

  @media screen and (max-width: 500px) {
    .tables {
      margin-top: 0em;
    }
  }

  .text-red {
    color: #d32f2f;
  }
  .text-green {
    color: #2e7d32;
  }

  #search_table {
    width: auto;
    padding: 10px 14px;
    font-size: 16px;
    border: 1px solid var(--grey);
    height: 42px;
    border-radius: 21px;
    background-color: var(--grey);
  }

  .search-container {
    display: flex;
    align-items: center;
    position: absolute;
    left: 0;
  }
  .date-range-display {
    width: auto;
    padding: 10px 14px;
    border: 1px solid var(--grey);
    border-radius: 21px;
    background-color: var(--grey);
  }

  .select-area-adresy {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-top: 2.5em;
    margin-bottom: 4em;
  }

  .select-date {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2em;
  }

  @media screen and (max-width: 850px) {
    .search-container {
      position: relative;
      left: auto;
      margin-bottom: 1em;
      flex-wrap: wrap;
    }

    .date-range-display {
      margin-top: 8px;
      margin-left: 0 !important;
    }
    .select-area-adresy {
      display: block;
    }
    .select-date {
      margin-top: 2em;
    }
  }
  .skeleton-group-header {
    padding: 8px 0;

    :deep(.v-skeleton-loader__button) {
      min-width: 36px;
      min-height: 36px;
      border-radius: 4px;
    }

    :deep(.v-skeleton-loader__text) {
      margin-top: 0;
    }
  }
  .compact-menu-card {
    :deep(.v-card) {
      padding: 0;
    }
  }

  .compact-list {
    padding: 0;

    :deep(.v-list-item) {
      min-height: unset;
      padding: 4px 8px;
    }

    :deep(.v-list-item__content) {
      padding: 4px 0;
    }
  }

  .compact-list-item {
    padding: 2px 0 !important;
  }

  .client-name {
    font-weight: 500;

    margin-left: 5px;
  }
  .deleted-status {
    color: #d32f2f;

    font-weight: bold;
  }

  .text-muted {
    color: #666;
    font-style: italic;
  }
  .change-type-label {
    font-weight: 500;
    display: inline-block;
  }

  .reconstructed-value {
    color: #d32f2f;
    font-weight: bold;
  }

  .ml-2 {
    margin-left: 0.5rem;
  }
  .reservation-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .client-name {
    font-weight: 500;
    color: var(--blue);
  }

  .reservation-id {
    color: #666;
    font-size: 0.9em;
  }

  .expanded-history {
    padding: 16px;
  }

  .history-table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      padding: 8px;
      border: 1px solid #ddd;
    }

    th {
      background-color: #f5f5f5;
      text-align: left;
    }
  }

  .change-type-label {
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;

    &.create {
      background-color: #e8f5e9;
      color: #2e7d32;
    }

    &.update {
      background-color: #e3f2fd;
      color: #1565c0;
    }

    &.delete {
      background-color: #ffebee;
      color: #d32f2f;
    }

    &.notification {
      background-color: #fff8e1;
      color: #f57f17;
    }
  }

  .performed-by-name-inline {
    font-size: 11px;
    color: #666;
    font-style: italic;
  }
</style>
<style>
  .v-data-table-footer__items-per-page > .v-select {
    width: 150px !important;
  }
</style>
