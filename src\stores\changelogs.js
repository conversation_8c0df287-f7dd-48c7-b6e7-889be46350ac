import { axiosInstance } from '/src/code/api';
import { defineStore } from 'pinia';
import { computed, reactive, ref } from 'vue';
import { useEmployee } from './employee.js';
import { handleToastError, handleToastSuccess } from '../utility/notification.js';
export const useChangelog = defineStore('changelog', () => {
  const useEmployeeStore = useEmployee();
  const getReservationChangelogs = reactive([]);
  const getSpecificReservationChangelog = ref({});
  const todaysReservationsChangelog = reactive([]);
  const office = computed(() => {
    return useEmployeeStore.myOffice;
  });

  // Translation mapping for changelog keys
  const keyTranslations = {
    // Client information
    id_client: 'ID klienta',
    id_service: 'ID služby',
    first_name: '<PERSON><PERSON>',
    last_name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    birth_year: 'Rok narodenia',
    phone_number: 'Telefónne číslo',

    // Reservation details
    date_of_reservation: 'Dátum rezervácie',
    start_time: '<PERSON>as za<PERSON>',
    end_time: 'Čas konca',
    description: 'Popis',
    reservation_status: 'Stav rezervácie',
    arrive_status: 'Stav príchodu',
    arrived_delay: 'Meškanie príchodu',
    deleted_status: 'Stav vymazania',
    id_deleted_user: 'ID užívateľa, ktorý vymazal',

    // Notifications
    notify_one_hour_before: 'Upozornenie hodinu pred',
    notify_morning: 'Ranné upozornenie',
    notify_day_before: 'Upozornenie deň pred',
    notify_2_days_before: 'Upozornenie 2 dni pred',
    email_notify: 'Emailové upozornenie',
    sms_notify: 'SMS upozornenie',
    sms_payed_by_client: 'SMS platená klientom',
    number_of_send_email: 'Počet odoslaných emailov',
    number_of_send_sms: 'Počet odoslaných SMS správ',

    // Consents
    consent_personal_data: 'Súhlas s osobnými údajmi',
    consent_terms_conditions: 'Súhlas s podmienkami',

    // Creation info
    created_by_assistant: 'Vytvorené asistentom',
    created_by_user: 'Vytvorené užívateľom',
    created_by_user_id: 'ID vytvárajúceho užívateľa',

    updated_at: 'Aktualizované dňa',

    // Other
    cancelHash: 'Hash zrušenia',
    unique_code: 'Unikátny kód',

    //Deleted
    canceled_at: 'Zrušené dňa',
    canceled_by: 'Zrušené užívateľom'
  };

  const valueTranslations = {
    // Reservation status
    'Created by User': 'Vytvorené užívateľom',
    'Created by user': 'Vytvorené užívateľom',
    'Created by Assistant': 'Vytvorené asistentom',
    Completed: 'Dokončené',
    'Canceled by User': 'Zrušené užívateľom',
    'Canceled by Client': 'Zrušené klientom',
    'Waiting for client payment': 'Čakanie na platbu klienta',

    // Arrive status
    'Waiting for Client Arrival': 'Čakanie na príchod klienta',
    'Waiting for client arrival': 'Čakanie na príchod klienta',
    'Arrived by system': 'Systémovo označené ako dorazené',
    Arrived: 'Klient dorazil',
    'Arrived by System': 'Systémovo označené ako dorazené',
    Missed: 'Zmeškaná',
    Canceled: 'Zrušené',
    // Deleted status
    'Not Deleted': 'Nevymazané',
    Deleted: 'Vymazané',
    'Client delayed': 'Klient mešká',
    // Deleted status
    'Deleted by User': 'Vymazané lekarom',
    'Deleted by Assistant': 'Vymazané asistentom',
    'Deleted by System': 'Vymazané systémom',
    'Deleted by Client': 'Vymazané klientom',
    'No show': 'Neprišiel',
    'Examined earlier': 'Vyšetrené skôr',
    'Examined later': 'Vyšetrené neskôr',
    'Waiting for sms confirmation': 'Čakanie na SMS potvrdenie',
    'Created by client': 'Vytvorené klientom',
    // Boolean values
    true: 'Áno',
    false: 'Nie',
    null: '-',
    '': '-',
    ' ': '-'
  };

  // Fields that should always be hidden from display
  const fieldsToHide = [
    'updated_at',
    'cancelHash',
    'unique_code',
    'id_deleted_user',
    'consent_personal_data',
    'consent_terms_conditions',
    'arrived_delay',
    // Internal fields that should not be displayed
    'action_type',
    'performed_by',
    'actor_info'
  ];

  // Fields related to attendance tracking that should be hidden
  const attendanceFields = ['arrived_delay'];

  // Check if a change is payment-related (from 'Waiting for payment' to any other state)
  function isPaymentCompletion(oldData, newData) {
    return (
      oldData?.reservation_status === 'Waiting for client payment' && newData?.reservation_status && newData.reservation_status !== 'Waiting for client payment'
    );
  }

  // Check if a change is reservation completion
  function isReservationCompletion(oldData, newData) {
    return oldData?.reservation_status !== 'Completed' && newData?.reservation_status === 'Completed';
  }

  // Check if a change is related to attendance tracking
  function isAttendanceChange(key, oldData, newData) {
    return attendanceFields.includes(key) || (key === 'arrive_status' && oldData?.arrive_status !== 'Canceled' && newData?.arrive_status !== 'Canceled');
  }

  // Translate a field key
  function translateKey(key) {
    return keyTranslations[key] || key;
  }

  // Translate a field value
  function translateValue(value, key) {
    // If value is boolean, convert to string first
    if (typeof value === 'boolean') {
      value = String(value);
    }

    // If value is null or undefined
    if (value === null || value === undefined) {
      return '-';
    }

    // Date formatting for date fields
    if ((key === 'date_of_reservation' || key === 'canceled_at') && value) {
      try {
        const date = new Date(value);
        return date.toLocaleDateString('sk-SK');
      } catch (e) {
        return value;
      }
    }

    // Time formatting for time fields
    if ((key === 'start_time' || key === 'end_time') && value) {
      return value.substring(0, 5); // Format HH:MM
    }

    // Check if there's a specific translation for this value
    return valueTranslations[value] || value;
  }

  // Get message for special change types
  function getSpecialChangeMessage(oldData, newData) {
    if (isPaymentCompletion(oldData, newData)) {
      return 'Služba bola uhradená';
    }

    if (isReservationCompletion(oldData, newData)) {
      return 'Rezervácia bola dokončená';
    }

    return null;
  }

  // Get changed fields, excluding hidden fields and handling special cases
  function getFilteredChangedFields(change) {
    const oldData = change.old_data || {};
    const newData = change.new_data || {};
    const fields = [];

    // Skip cancelHash and other hidden fields
    for (const key in newData) {
      // Skip fields that start with underscore (internal fields)
      if (key.startsWith('_')) continue;

      // Skip fields that should always be hidden
      if (fieldsToHide.includes(key)) continue;

      // Skip attendance tracking fields
      if (isAttendanceChange(key, oldData, newData)) continue;

      // If value changed, add to the list
      if (JSON.stringify(oldData[key]) !== JSON.stringify(newData[key])) {
        fields.push(key);
      }
    }

    return fields;
  }

  // Check if a change should be displayed (excludes purely technical changes)
  function shouldDisplayChange(change) {
    // Get special message if any
    const specialMessage = getSpecialChangeMessage(change.old_data, change.new_data);
    if (specialMessage) return true;

    // Check if there are visible changed fields
    const changedFields = getFilteredChangedFields(change);
    return changedFields.length > 0;
  }

  // Filter out unwanted keys from data object
  function filterChangelogData(data) {
    if (!data) return {};
    const filtered = {};
    for (const [key, value] of Object.entries(data)) {
      // Skip fields that start with underscore (internal fields)
      if (key.startsWith('_')) continue;

      // Skip fields that should always be hidden
      if (!fieldsToHide.includes(key)) {
        filtered[key] = value;
      }
    }
    return filtered;
  }

  // Function to format a changelog field for display
  function formatChangelogField(key, value) {
    const translatedKey = translateKey(key);
    const translatedValue = translateValue(value, key);
    return { key: translatedKey, value: translatedValue };
  }

  // Helper for performed_by translation
  function getPerformedByLabel(performed_by) {
    if (!performed_by || !performed_by.type) return '-';
    switch (performed_by.type) {
      case 'system':
        return 'Systém';
      case 'user':
        return 'Používateľ';
      case 'doctor':
        return 'Lekár';
      case 'client':
        return 'Klient';
      default:
        return '-';
    }
  }

  async function getReservationChangelog(id_reservation) {
    try {
      const response = await axiosInstance.get(`clientReservation/${id_reservation}/logs`);
      getSpecificReservationChangelog.value = {};
      getSpecificReservationChangelog.value = response.data.data;
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní historie rezervácie');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function getAllReservationsChangelogs(from, to, page = 1, limit = 25) {
    try {
      if (!office.value) return;
      const formattedFrom = from.toISOString().split('T')[0];
      const formattedTo = to.toISOString().split('T')[0];
      const params = {
        start_date: formattedFrom,
        end_date: formattedTo,
        page: page,
        limit: limit
      };

      const response = await axiosInstance.get(`clientReservation/office/${office.value}/logs`, {
        params
      });
      getReservationChangelogs.splice(0, getReservationChangelogs.length);
      getReservationChangelogs.push(...response.data.data.items);
      if (response && response.status === 200) {
        return {
          total: response.data.data.total_count,
          total_count: response.data.data.total_count,
          current_page: response.data.data.current_page,
          total_pages: response.data.data.total_pages,
          items: response.data.data.items
        };
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní historie rezervácií');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  async function getAllReservationsForTodayChangeLog() {
    try {
      if (!office.value) return;

      const params = {
        start_date: new Date().toISOString().split('T')[0] + 'T00:00:00',
        end_date: new Date().toISOString().split('T')[0] + 'T23:59:59',
        page: 1,
        limit: 25
      };

      const response = await axiosInstance.get(`clientReservation/office/${office.value}/logs`, {
        params
      });
      todaysReservationsChangelog.splice(0, todaysReservationsChangelog.length);
      todaysReservationsChangelog.push(...response.data.data.items);
      if (response && response.status === 200) {
        return response;
      }
      return false;
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.response?.data?.error || error?.message;
      handleToastError(errorMessage, 'Nastala chyba pri načítaní historie rezervácií');
      return {
        error: error.response?.data ?? 'Unknown error'
      };
    }
  }

  return {
    getAllReservationsChangelogs,
    getReservationChangelog,
    getReservationChangelogs,
    getSpecificReservationChangelog,
    getAllReservationsForTodayChangeLog,
    todaysReservationsChangelog,
    translateKey,
    translateValue,
    formatChangelogField,
    filterChangelogData,
    getFilteredChangedFields,
    getSpecialChangeMessage,
    shouldDisplayChange,
    isAttendanceChange,
    isPaymentCompletion,
    isReservationCompletion,
    getPerformedByLabel,
    keysToFilter: fieldsToHide
  };
});
