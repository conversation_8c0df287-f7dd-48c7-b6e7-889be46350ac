<template>
  <div class="content">
    <loading-overlay :loading="loading" />
    <div v-if="filteredChangeLogs.length > 0" class="latest-changes-section">
      <div class="latest-changes-list">
        <v-card v-for="change in filteredChangeLogs" :key="change.id_log" class="change-card">
          <v-card-title class="change-card-title">
            <span class="change-time">{{ formatDateTime(change.timestamp) }}</span>
            <span class="change-reservation">
              {{ getFormattedClientName(change) }}
            </span>
          </v-card-title>
          <v-card-text>
            <div class="change-user" v-if="change.doctor_name">
              <span>
                <b>Užívateľ:</b>
                {{ change.doctor_name }}
              </span>
            </div>
            <div class="change-user" v-if="change.service_name">
              <span>
                <b>Služba:</b>
                {{ change.service_name }}
              </span>
            </div>
            <div class="change-performed-by">
              <span>
                <b>Kto vykonal zmenu:</b>
                <span class="performed-by-badge">{{ getPerformedByLabel(change.performed_by) }}</span>
                <span v-if="change.performed_by && change.performed_by.name">: {{ change.performed_by.name }}</span>
              </span>
            </div>
            <div class="change-details">
              <div class="change-fields">
                <span>
                  <b>{{ getChangeTypeLabel(change) }}</b>
                </span>

                <div v-if="getChangeType(change) === 'create'" class="change-field-group">
                  <div v-for="(value, key) in getRelevantCreationFields(change.new_data)" :key="key" class="change-field">
                    <div class="field-name">{{ useChangelogStore.translateKey(key) }}</div>
                    <div class="field-values">
                      <span class="new-value-create">
                        {{ formatValue(useChangelogStore.translateValue(value, key)) }}
                      </span>
                    </div>
                  </div>
                </div>

                <div v-else-if="getChangeType(change) === 'delete'" class="change-field-group">
                  <div class="change-message">
                    <b class="deleted-status">
                      {{ useChangelogStore.translateValue(change.new_data.deleted_status, 'deleted_status') }}
                    </b>
                  </div>

                  <div v-if="Object.keys(getReconstructedData(change)).length > 0" class="reconstructed-data-section">
                    <b>Údaje rezervácie pred zmazaním:</b>
                    <div v-for="(value, key) in getReconstructedData(change)" :key="key" class="change-field">
                      <div class="field-name">{{ useChangelogStore.translateKey(key) }}</div>
                      <div class="field-values">
                        <span class="reconstructed-value">
                          {{ formatValue(useChangelogStore.translateValue(value, key)) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Special state changes with fixed messages -->
                <div v-else-if="specialChangeMessage(change)" class="change-message">
                  <b class="new-value" style="font-style: normal">{{ specialChangeMessage(change) }}</b>
                </div>
                <template v-else>
                  <div v-for="(field, index) in getVisibleChangedFields(change)" :key="index" class="change-field">
                    <div class="field-name">{{ useChangelogStore.translateKey(field) }}</div>
                    <div class="field-values">
                      <span class="old-value">
                        <b>Pôvodne:</b>
                        {{ formatValue(useChangelogStore.translateValue(change.old_data[field], field)) }}
                      </span>
                      <span class="arrow">→</span>
                      <span class="new-value">
                        <b>Nová hodnota:</b>
                        {{ formatValue(useChangelogStore.translateValue(change.new_data[field], field)) }}
                      </span>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </div>
    </div>
    <div v-else class="no-changes-message">
      <p>Neboli vykonané žiadne zmeny rezervácií</p>
    </div>
  </div>
</template>
<script setup>
  import { computed, onMounted, ref } from 'vue';
  import { useChangelog } from '../../stores/changelogs.js';

  const useChangelogStore = useChangelog();
  const emits = defineEmits(['closeModal']);
  const clientNameMap = ref({});
  const loading = ref(true);
  // Store reconstructed reservation data for each reservation ID
  const reservationStates = ref({});

  onMounted(async () => {
    buildReservationStates();
    loading.value = false;
  });

  const reservationChangeLog = computed(() => {
    if (useChangelogStore.getSpecificReservationChangelog) {
      return [...useChangelogStore.getSpecificReservationChangelog].reverse();
    } else return [];
  });

  // Filter out entries where both old_data and new_data are empty
  const filteredChangeLogs = computed(() => {
    return reservationChangeLog.value.filter((log) => {
      // Always show creation and deletion events
      if (getChangeType(log) === 'create' || getChangeType(log) === 'delete') {
        return true;
      }

      // For other events, check if they contain meaningful changes
      return useChangelogStore.shouldDisplayChange(log);
    });
  });

  const getVisibleChangedFields = (change) => {
    return useChangelogStore.getFilteredChangedFields(change);
  };

  // Get special message for certain change types
  const specialChangeMessage = (change) => {
    return useChangelogStore.getSpecialChangeMessage(change.old_data, change.new_data);
  };

  // Build the complete state of each reservation at each point in time
  const buildReservationStates = () => {
    if (!useChangelogStore.getSpecificReservationChangelog) return;

    // Get all logs for this reservation, sorted chronologically (oldest first)
    const sortedLogs = [...useChangelogStore.getSpecificReservationChangelog].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    const id = sortedLogs[0]?.table_primary_key;
    if (!id) return;

    // Initialize state object for this reservation
    reservationStates.value[id] = {};

    // Process each log in chronological order
    sortedLogs.forEach((log) => {
      // Use the new log_type field to determine type
      if (log.log_type === 'Created') {
        // Start with a clean slate for each creation event
        reservationStates.value[id] = {};

        // Add all non-null values from the creation event
        for (const [key, value] of Object.entries(log.new_data)) {
          if (value !== null && value !== '') {
            reservationStates.value[id][key] = value;
          }
        }
      }
      // For updates and notifications, update the specific fields
      else if (log.log_type === 'Updated' || log.log_type === 'Notified') {
        for (const [key, value] of Object.entries(log.new_data)) {
          // Update the state with the new value
          reservationStates.value[id][key] = value;
        }
      }
    });
  };

  // Get the reconstructed reservation data at the time of deletion
  const getReconstructedData = (deleteLog) => {
    const id = deleteLog.table_primary_key;
    const result = {};

    // If we have built the state for this reservation
    if (reservationStates.value[id]) {
      // Get all non-system fields that have non-null values
      const relevantFields = getRelevantFields(reservationStates.value[id]);

      // Return the relevant fields
      return relevantFields;
    }

    return result;
  };

  // Get relevant fields (non-system, non-null) from a data object
  const getRelevantFields = (data) => {
    const relevantFields = {};

    // Use the fieldsToHide from the store
    const ignoredFields = [
      ...useChangelogStore.keysToFilter,
      'id_client',
      'id_service',
      'number_of_send_email',
      'number_of_send_sms',
      'created_by_assistant',
      'created_by_user_id',
      'deleted_status',
      'reservation_status',
      'arrive_status',
      'canceled_at'
    ];

    for (const key in data) {
      if (!ignoredFields.includes(key) && data[key] !== null && data[key] !== '') {
        relevantFields[key] = data[key];
      }
    }

    return relevantFields;
  };

  // Replace the current getChangeType function with this:
  const getChangeType = (change) => {
    // Use the direct log_type field from the backend
    switch (change.log_type) {
      case 'Created':
        return 'create';
      case 'Canceled':
        return 'delete';
      case 'Notified':
        return 'notification';
      case 'Updated':
      default:
        return 'update';
    }
  };

  // Replace the current getChangeTypeLabel function with this:
  const getChangeTypeLabel = (change) => {
    switch (change.log_type) {
      case 'Created':
        return 'Vytvorenie rezervácie';
      case 'Canceled':
        return 'Zmazanie rezervácie';
      case 'Notified':
        return 'Poslanie notifikácie';
      case 'Updated':
        return 'Aktualizovanie údajov rezervácie';
      default:
        return 'Zmenené:';
    }
  };

  // Get relevant fields for creation display (excluding system fields and null values)
  const getRelevantCreationFields = (newData) => {
    const relevantFields = {};

    // Use the fieldsToHide from the store
    const ignoredFields = [
      ...useChangelogStore.keysToFilter,
      'id_client',
      'id_service',
      'number_of_send_email',
      'number_of_send_sms',
      'unique_code',
      'created_by_assistant',
      'created_by_user_id'
    ];

    for (const key in newData) {
      if (!ignoredFields.includes(key) && newData[key] !== null && newData[key] !== '') {
        relevantFields[key] = newData[key];
      }
    }

    return relevantFields;
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('sk-SK', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatValue = (value) => {
    if (value === null || value === undefined) {
      return 'Prázdne';
    } else if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return value.toString();
  };
  const getFormattedClientName = (log) => {
    if (log.first_name && log.last_name) {
      return `${log.first_name} ${log.last_name}`;
    }
    return `ID: ${log.table_primary_key}`;
  };

  // Add helper for performed_by translation
  const getPerformedByLabel = (performed_by) => {
    if (!performed_by || !performed_by.type) return '-';
    switch (performed_by.type) {
      case 'system':
        return 'Systém';
      case 'user':
        return 'Používateľ';
      case 'doctor':
        return 'Lekár';
      case 'client':
        return 'Klient';
      default:
        return '-';
    }
  };
</script>

<style lang="scss" scoped>
  .modal-btn-confirm {
    background-color: var(--blue);
    border: 0;
    padding: 20px 20px 20px 20px;
    color: white;
    border-radius: 30px;
    text-align: center;
    font-size: 12px;
    line-height: 15px;
    text-transform: uppercase;
    margin-top: 2.5em;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-right: 0.5em;
    width: 100px;
  }

  .modal-btn-confirm:hover {
    cursor: pointer;
    opacity: 0.7;
  }

  .modal-background-confirm {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    z-index: 99;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .modal-confirm {
    right: 0;
    background: white;
    padding: 2em;
    position: fixed;
    margin: auto;
    max-width: 55em;
    top: 5em;
    left: 0;
    width: 55em;
    max-height: 80vh; /* Increased from 40em to 80vh for better scaling */
    z-index: 9999;
    border-radius: 20px;
    overflow-y: auto;
  }
  .close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    cursor: pointer;
  }

  .modal-confirm-enter-active,
  .modal-confirm-leave-active {
    transition: 0.2s ease-out;
  }

  .modal-confirm-enter-from,
  .modal-confirm-leave-to {
    opacity: 0;
    transition: 0.2s ease-out;
  }
  .modal-container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .modal-container-footer {
    display: flex;
    justify-content: space-between;
  }
  .release-notes-container {
    max-height: 35vh;
    overflow-y: auto;
    padding: 1em;
    margin-top: 2em;
  }

  .release-card {
    margin-bottom: 1em;
    padding: 1em;
    border-radius: 10px;
  }

  .latest-changes-section {
    margin-top: 1em;
    margin-bottom: 6em;
  }

  .latest-changes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
  }

  .view-all-btn {
    background-color: transparent;
    border: none;
    color: var(--blue);
    font-weight: 600;
    cursor: pointer;
    padding: 5px 10px;
    font-size: 14px;

    &:hover {
      text-decoration: underline;
    }
  }

  .latest-changes-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .change-card {
    border-radius: 8px;
    padding: 8px;

    display: flex;
    flex-direction: column;
  }
  .change-card .v-card-text {
    overflow-y: auto;
    flex-grow: 1;
    padding-bottom: 8px; /* Reduce bottom padding */
  }
  .change-card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    padding: 0px 0px 8px 16px;
    flex-shrink: 0; /* Prevent the title from shrinking */
  }

  .change-time {
    font-weight: bold;
    color: var(--blue);
  }

  .change-reservation {
    background-color: #f5f5f5;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .change-user {
    margin-bottom: 8px;
    font-size: 14px;
  }

  .change-performed-by {
    margin-bottom: 8px;
    font-size: 14px;
  }

  .change-details {
    font-size: 14px;
  }

  .change-fields {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .change-field {
    background-color: #f5f5f5;
    padding: 8px 12px;
    border-radius: 4px;
    margin-top: 8px;
  }

  .field-name {
    font-weight: bold;
    margin-bottom: 4px;
  }

  .field-values {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
  }

  .old-value {
    color: #d32f2f;
    padding: 2px 4px;
    border-radius: 3px;
  }

  .new-value {
    color: #2e7d32;
    padding: 2px 4px;
    border-radius: 3px;
  }

  .arrow {
    font-weight: bold;
    color: #666;
  }

  .no-changes-message {
    text-align: center;
    margin: 2em 0 6em 0;
    color: #666;
  }

  .no-changes {
    color: #666;
    font-style: italic;
  }

  .top {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 1.5em;
    margin-top: -2.5em;
  }

  .top-line {
    width: 99%;
    text-align: left;
    margin-left: 0;
    margin-top: 1.5em;
    margin-bottom: 2em;
  }

  hr.top-line {
    color: #e1e1e1;
    border: 1px solid;
  }

  .redirect-btn {
    background-color: var(--blue);
    border: 0;
    padding: 15px;
    color: white;
    border-radius: 30px;
    text-align: center;
    font-size: 12px;
    line-height: 15px;
    text-transform: uppercase;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 160px;
  }
  .redirect-btn:hover {
    cursor: pointer;
    opacity: 0.7;
  }

  .reconstructed-data-section {
    margin-top: 16px;
    padding: 12px;
    border-radius: 4px;
    border-left: 3px solid #d32f2f;
  }

  .reconstructed-value {
    color: #d32f2f;
    font-weight: bold;
  }

  .change-field-group {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .new-value-create {
    color: #2e7d32;
    font-weight: bold;
  }

  .deleted-status {
    color: #d32f2f;
    font-style: normal;
  }

  .change-message {
    margin-top: 10px;
    padding: 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-style: italic;
  }
  .service-badge {
    background-color: #e3f2fd;
    color: var(--blue);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
  }

  .performed-by-badge {
    margin-left: 4px;
  }
</style>
